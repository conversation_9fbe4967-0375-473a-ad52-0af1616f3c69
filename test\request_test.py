# -*- coding: utf-8 -*-
import requests
import time

url = 'https://dldir1v6.qq.com/qqfile/qq/QQNT/Windows/QQ_9.9.20_250616_x64_01.exe'


def download_file(url):
  resp = requests.get(url,stream=True)
  downloaded_size = 0
  # print(resp.headers)

  with open('QQ.exe', mode='wb') as f:
    for chunk in resp.iter_content(chunk_size=1024*1024*10):
      # time.sleep(0.5)
      downloaded_size += len(chunk)
      percent = (downloaded_size / int(resp.headers['Content-Length']))
      print(f"下载进度：{percent:.2%}")
      f.write(chunk)


def test_session():
  session = requests.Session()
  url = "https://www.baidu.com"
  resp = session.get(url)
  print("第一次请求的header",resp.request.headers)
  print("第一次响应的header",resp.headers)
  resp2 = session.get(url)
  print("第二次请求的header",resp2.request.headers)
  print(resp.text)


def test_proxy():
  proxy = {
    "http": "http://127.0.0.1:9999",
    "https": "http://127.0.0.1:9999",
  }
  resp = requests.get(url,proxies=proxy)
  print(resp.text)

if __name__ == '__main__':
  # download_file(url)
  # test_session()
  test_proxy()
