import requests
from retrying import retry
import random


# 获取cookies
def get_cookies():
    headers = {
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
    }
    url = 'http://www.baidu.com'
    resp = requests.get(url,headers=headers)
    # cookies的类型
    print(type(resp.cookies))
    # 将cookies转换为字典
    cookies = requests.utils.dict_from_cookiejar(resp.cookies)
    print(cookies)

def baidu_redirect():
    headers = {
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
    }
    url = 'http://www.baidu.com'
    resp = requests.get(url,headers=headers)
    # 实际的url
    print(resp.url)
    # 是否发生了重定向
    if resp.history:
        for r in resp.history:
            print(r.url)
            print(r.status_code)
    # print(resp.history)
def baidu_redirect_cancel():
    headers = {
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
    }
    url = 'http://www.baidu.com'
    resp = requests.get(url,headers=headers,allow_redirects=False)
    # 实际的url
    print(resp.url)
    # 是否发生了重定向
    if resp.history:
        for r in resp.history:
            print(r.url)
            print(r.status_code)
    else:
        print('没有发生重定向')

def ignore_ssl():
    url = 'https://12306.cn/mormhweb/'
    resp = requests.get(url, verify=False)
    print(resp.text)

# retrying 模块
@retry(stop_max_attempt_number=3)
def retry_method():
    random_num = random.randint(0,10)
    if random_num > 5:
        print(f'随机数为{random_num}，大于5，将抛出异常并重试') 
        raise Exception('随机数大于5')
    else:
        print(f'随机数为{random_num}')

# cookies = requests.utils.dict_from_cookiejar(resp.cookies)
# print(cookies)
if __name__ == '__main__':
    # resp cookies
    # get_cookies()
    
    # baidu_redirect()
    
    # baidu_redirect_cancel()
    # ignore_ssl()
    try:
        retry_method()
    except Exception as e:
        print(e)