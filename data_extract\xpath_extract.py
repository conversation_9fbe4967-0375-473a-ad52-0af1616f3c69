from lxml import etree

def etree_convert():
  text = ''' <div> <ul> 
          <li class="item-1"><a href="link1.html">first item</a></li> 
          <li class="item-1"><a href="link2.html">second item</a></li> 
          <li class="item-inactive"><a href="link3.html">third item</a></li> 
          <li class="item-1"><a href="link4.html">fourth item</a></li> 
          <li class="item-0"><a href="link5.html">fifth item</a> 
          </ul> </div> '''

  html = etree.HTML(text)
  print(type(html))

  print(etree.tostring(html).decode())


def get_attr():
    text = ''' <div> <ul> 
          <li class="item-1"><a>first item</a></li> 
          <li class="item-1"><a href="link23.html" href="link23.html">second item</a></li> 
          <li class="item-inactive"><a href="link3.html"></a></li> 
          <li class="item-1"><a href="link4.html">fourth item</a></li> 
          <li class="item-0"><a href="link5.html">fifth item</a> 
          </ul> </div> '''
    html = etree.HTML(text)
    # 获取直接打印
    # href_list = html.xpath('//li//a/@href')
    # text_list = html.xpath('//li//a/text()')
    # print(href_list)
    # print(text_list)
    # 先获取li标签，然后打印

    li_list = html.xpath('//li')
    for li in li_list:
      item = {}
      item['href'] = li.xpath('./a/@href')[0] if len(li.xpath('./a/@href')) > 0 else None
      item['text'] = li.xpath('./a/text()')[0] if len(li.xpath('./a/text()')) > 0 else None
      print(item)

    
if __name__ == "__main__":
  get_attr()
