<!doctype html>
<html data-path="/">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="og:url" content="https://m.36kr.com/"><meta name="og:type" content="website"><meta property="article:published_time" content="2025-07-25T11:35:03+08:00"><meta data-react-helmet="true" name="baidu-site-verification" content="ET7tYDCqIv"/><meta data-react-helmet="true" name="shenma-site-verification" content="8d43b014716644e38cc8fff2051c47a0_1622786801"/><meta data-react-helmet="true" name="360-site-verification" content="b14201a8708468357bd6fab4ad556b78"/><meta data-react-helmet="true" name="sogou_site_verification" content="5ZZdpkOf3U"/><meta data-react-helmet="true" name="baidu-site-verification" content="code-qZf2fMAXWA"/><meta data-react-helmet="true" name="renderer" content="webkit"/><meta data-react-helmet="true" name="apple-mobile-web-app-title" content="Title"/><meta data-react-helmet="true" name="apple-mobile-web-app-capable" content="yes"/><meta data-react-helmet="true" name="apple-mobile-web-app-status-bar-style" content="black"/><meta data-react-helmet="true" name="HandheldFriendly" content="True"/><meta data-react-helmet="true" name="MobileOptimized" content="320"/><meta data-react-helmet="true" name="applicable-device" content="mobile"/><meta data-react-helmet="true" name="format-detection" content="telephone=no"/><meta data-react-helmet="true" property="og:description" content="36氪通过全面，独家的视角为用户深度剖析最前沿的资讯，致力于让一部分人先看到未来，内容涵盖快讯，科技，金融，投资，房产，汽车，互联网，股市，教育，生活，职场等，秉承着新商业媒体人的使命砥砺前行"/><meta data-react-helmet="true" property="og:image" content="https://img.36krcdn.com/20191024/v2_1571894049839_img_jpg"/><meta data-react-helmet="true" property="og:title" content="36氪_让一部分人先看到未来"/><meta data-react-helmet="true" name="description" content="36氪通过全面，独家的视角为用户深度剖析最前沿的资讯，致力于让一部分人先看到未来，内容涵盖快讯，科技，金融，投资，房产，汽车，互联网，股市，教育，生活，职场等，秉承着新商业媒体人的使命砥砺前行"/><meta data-react-helmet="true" name="keywords" content="快讯,直播,8点一氪,地方站,深氪,每日商业精选,新商业,热榜,36氪首发,融资, 科技,电子商务,股票,投资,市场营销,移动互联网,互联网电商,芯片,科技新闻,创业,,人工智能,汽车产业,新能源汽车,医疗,风投,直播带货,小程序,新基建,新经济,区块链"/>
    <title data-react-helmet="true">36氪_让一部分人先看到未来</title>
    <link href="//static.36krcdn.com/36kr-mobile/static/app.e4d7b4bd.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="//static.36krcdn.com/36kr-mobile/static/home.4b0f74bd.css">
    
    <link data-react-helmet="true" href="/favicon.ico" rel="shortcut icon" type="image/vnd.microsoft.icon"/><link data-react-helmet="true" rel="dns-prefetch" href="//sta.36krcdn.com"/><link data-react-helmet="true" rel="dns-prefetch" href="//pic.36krcdn.com"/><link data-react-helmet="true" rel="dns-prefetch" href="//static.36krcdn.com"/><link data-react-helmet="true" rel="dns-prefetch" href="//img.36krcdn.com"/><link data-react-helmet="true" rel="dns-prefetch" href="//file.36krcdn.com"/><link data-react-helmet="true" rel="canonical" href="https://m.36kr.com/"/><script data-react-helmet="true" >
      window.WEIXINSHARE = {
        shareTitle: "36氪_让一部分人先看到未来",
        shareImg: "https://img.36krcdn.com/20191024/v2_1571894049839_img_jpg",
        imgUrl: "https://img.36krcdn.com/20191024/v2_1571894049839_img_jpg",
        shareDesc: "36氪为您提供创业资讯、科技新闻、投融资对接、股权投资、极速融资等创业服务，致力成为创业者可以依赖的创业服务平台，为创业者提供最好的产品和服务。"
      }
    </script><script data-react-helmet="true" >
      // sem 统计代码
      var _hmt= _hmt||[];
      (function(){
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?1684191ccae0314c6254306a8333d090";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm,s);
      })();
    </script><script data-react-helmet="true" >
      //百度统计代码
      var _hmt = _hmt || [];
      (function () {
          var hm = document.createElement('script');
          hm.src = "https://hm.baidu.com/hm.js?713123c60a0e86982326bae1a51083e1";
          var s = document.getElementsByTagName('script')[0];
          s.parentNode.insertBefore(hm, s);
      })();
    </script><script data-react-helmet="true" >
        // 给百度seo主动推送,  文明确认
        (function () {
          var bp = document.createElement('script');
          var curProtocol = window.location.protocol.split(':')[0];
          if (curProtocol === 'https') {
            bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';
          }
          else {
            bp.src = 'http://push.zhanzhang.baidu.com/push.js';
          }
          var s = document.getElementsByTagName("script")[0];
          s.parentNode.insertBefore(bp, s);
        })();
      </script><script data-react-helmet="true" src="https://static.geetest.com/static/tools/gt.js"></script><script data-react-helmet="true" >
          // https://file.36krcdn.com/20190829/v2_1567049576679_file_js 修改引用方式 添加时间:2022-8-22
          (function() {
            function isNumber (path) {
              return !isNaN(Number(path));
            }
            
            function isObj (key) {
              return typeof key === 'object';
            }
            
            /**
             * 判断obj中是否有key这个属性
             * @param obj {object}
             * @param key {string}
             */
            function objHasKey(obj, key) {
              return obj.hasOwnProperty(key);
            }
            
            /**
             * @param {{
              *  control: Boolean,
              *  key: string,
              *  value: string,
              *  data?: obj,
              *  elseValue?: string
              * }} option
              * 
              * 参数说明：
              * control 判断条件
              * data 最终返回的对象
              * key 添加到data中的属性
              * value 要添加的值
              * elseValue 如果control不成立，添加到data的值 传入此参数则添加，不传则不会添加
              */
            function setProperty(option) {
              const { data = {}, key, value, control, elseValue } = option;
              if (control) {
                data[key] = value;
              } else if (elseValue) {
                data[key] = elseValue;
              }
              return data;
            }
            
            function externalLink (hrefValue) {
              const data = {};
              const externalLinkList = [
                { pathKey: '/', mediaSourceValue: 'page_home' },
                { pathKey: 'p', mediaSourceValue: 'article' },
                { pathKey: 'newsflashes', mediaSourceValue: 'newsflash', subPathNotNumMediaSourceValue: 'page_newsflashes' },
                { pathKey: 'topics', mediaSourceValue: 'topics' },
              ];
              const pathInfo = new PathInfo(window.location.pathname);
              // 记录外链url
              data.media_content_id = hrefValue;
              // 详情页中（当前地址包含ID），含有外链，发送source_id
              Object.assign(data, setProperty({
                control: isNumber(pathInfo.sub),
                key: 'source_id',
                value: pathInfo.sub,
              }));
              // 地方站 外链记录media_event_value
              if (pathInfo.root === 'local') {
                data.media_event_value = 'local_' + pathInfo.sub;
              }
              return Object.assign(data, genMediaSource(externalLinkList, isNumber(pathInfo.sub)));
            }
            
            function internalLink (hrefValue, className) {
              const pageInfo = new PageInfo(hrefValue);
              const data = {};
              /* 聚合页跳转id */
              Object.assign(data, setProperty({
                control: pageInfo.currentPath.sub && isNumber(pageInfo.currentPath.sub) && pageInfo.destPath.sub && isNumber(pageInfo.destPath.sub),
                key: 'source_id',
                value: pageInfo.currentPath.sub,
              }));
              /* 搜索结果、资讯、地方 */
              Object.assign(data, genMediaEventValue(hrefValue));
              // 文章详情含有跳转链接
              Object.assign(data, setProperty({
                control: pageInfo.currentPath.root === 'p' && isNumber(pageInfo.currentPath.sub) && isNumber(pageInfo.destPath.sub),
                key: 'media_source',
                value: 'article',
              }));
              /* 不同页面，进入详情页 */
              Object.assign(data, intoDetailPage(pageInfo.destPath.sub));
              /* 添加media_conten_type 针对相应页面添加属性 */
              Object.assign(data, genContentTypeAndId(hrefValue, className));
              return data;
            }
            
            function genMediaEventValue (hrefValue) {
              const data = {};
              const key = 'media_event_value';
              const pageInfo = new PageInfo(hrefValue);
              const genMediaEventValueList = [
                { pathKey: 'search', subPathList: [
                  {key: 'articles', value: 'search_articles'},
                  {key: 'newsflashes', value: 'search_flashnews'},
                  {key: 'video', value: 'search_videos'},
                  {key: 'monographic', value: 'search_topics'},
                  {key: 'user', value: 'search_writer'},
                ]},
                { pathKey: 'information', subPathList: [
                  'web_news', 'contact', 'enterpriseservice', 'happy_life', 'travel',
                  'real_estate', 'web_recommend', 'technology', {key: 'zhichang', value: 'web_zhichang'}, 'innovate',
                ]},
                { pathKey: 'local' }
              ];
              for (const pathItem of genMediaEventValueList) {
                if (pageInfo.currentPath.root === pathItem.pathKey) {
                  if (pageInfo.currentPath.root === 'local') {
                    // 城市
                    data.media_event_value = 'local_' + pageInfo.currentPath.sub;
                  } else {
                    for (const subPathItem of pathItem.subPathList) {
                      const value = isObj(subPathItem) ? subPathItem.value : subPathItem;
                      const subPathKey = isObj(subPathItem) ? subPathItem.key : subPathItem;
                      if (pageInfo.currentPath.sub === subPathKey) {
                        Object.assign(data, setProperty({
                          control: isNumber(pageInfo.destPath.sub),
                          key,
                          value,
                        }));
                      }
                    }
                  }
                }
              }
              return data;
            }
            
            function intoDetailPage (subHref) {
              // 不同页面进入到详情页，添加media_source
              const pathInfo = new PathInfo(window.location.pathname);
              const intoDetailPageList = [
                { pathKey: '/', mediaSourceValue: 'page_home' },
                { pathKey: '/video', mediaSourceValue: 'page_video' },
                { pathKey: '/topics', mediaSourceValue: 'page_topics' },
                { pathKey: 'newsflashes', control: isNumber(pathInfo.sub), mediaSourceValue: 'newsflash', subPathNotNumMediaSourceValue: 'page_newsflashes' },
                { pathKey: 'information', mediaSourceValue: 'page_information' },
                { pathKey: 'search', mediaSourceValue: 'page_search' },
                { pathKey: 'Calendar', mediaSourceValue: 'calendar' },
                { pathKey: 'user', mediaSourceValue: 'user' },
                { pathKey: 'motif', mediaSourceValue: 'motif' },
                { pathKey: 'topics', mediaSourceValue: 'topics' },
                { pathKey: 'video', mediaSourceValue: 'video' },
              ];
              return genMediaSource(intoDetailPageList, isNumber(subHref));
            }
            
            function genMediaSource (pathList, control) {
              const pathInfo = new PathInfo(window.location.pathname);
              const key = 'media_source';
              const data = {};
              for (const pathItem of pathList) {
                if (pathInfo.path === pathItem.pathKey) {
                  data.media_source = pathItem.mediaSourceValue;
                } else if (pathInfo.root === pathItem.pathKey && pathInfo.sub) {
                  Object.assign(data, setProperty({
                    control: objHasKey(pathItem, 'control') ? pathItem.control : control,
                    key,
                    value: pathItem.mediaSourceValue,
                    elseValue: pathItem.subPathNotNumMediaSourceValue || null,
                  }));
                }
              }
              return data;
            }
            
            function genContentTypeAndId (hrefValue, className) {
              // 设置media_content_type和media_content_id
              const pageInfo = new PageInfo(hrefValue);
              const genContentTypeAndIdList = [
                {
                  key: 'p',
                  setData: {
                    media_content_type: 'article',
                    media_content_id: pageInfo.destPath.sub,
                  }
                },
                {
                  key: 'newsflashes',
                  setData: {
                    media_content_type: 'flashnews',
                    media_content_id: pageInfo.destPath.sub,
                  }
                },
                {
                  key: 'video',
                  setData: {
                    media_content_type: 'video',
                    media_content_id: pageInfo.destPath.sub,
                  }
                },
                {
                  key: 'v-video',
                  setData: {
                    media_content_type: 'video_portrait',
                    media_content_id: pageInfo.destPath.sub,
                  }
                },
                {
                  key: 'topics',
                  setData: {
                    media_content_type: 'topic',
                    media_content_id: pageInfo.destPath.sub,
                  }
                },
                {
                  key: 'motif',
                  setData: {
                    media_content_type: (() => {
                      // TODO 这里声明了一个自执行函数，判断是否为商业主题后，给media_conten_type不同的值，可否优化?
                      const isMotifList = pageInfo.currentPath.root === 'motif-catalog' && pageInfo.currentPath.sub === 'recomend';
                      const isHomeEntry = className.includes('businessMotifHome-main-item-top-a');
                      const isBusinessMotif = isMotifList || isHomeEntry;
                      return isBusinessMotif ? 'business_motif' : 'motif';
                    })(),
                    media_content_id: pageInfo.destPath.sub,
                  }
                },
                {
                  key: 'user',
                  setData: {
                    media_content_type: 'user',
                    media_content_id: pageInfo.destPath.sub,
                  }
                },
              ];
              let data = {};
              if (!isNumber(pageInfo.destPath.sub)) return data;
              for (const hrefItem of genContentTypeAndIdList) {
                if (pageInfo.destPath.root === hrefItem.key) {
                  data = hrefItem.setData;
                }
              }
              return data;
            }
            
            function genPropertyAccordingToAttribute (elementTarget) {
              /**
               * 方法依赖a标签上设置的属性，根据a标签的属性添加
               */
              const operation_list = elementTarget.getAttribute('sensors_operation_list');
              const index_num = elementTarget.getAttribute('sensors_index_num');
              const operate_type = elementTarget.getAttribute('sensors_operate_type');
              const position_id = elementTarget.getAttribute('sensors_position_id');
              const plan_id = elementTarget.getAttribute('sensors_media_content_id');
              const genPropertyAccordingToAttributeList = [
                {
                  control: operation_list === 'page_flow' && window.location.pathname === '/',
                  key: 'operation_list',
                  value: 'latest_articles',
                  elseValue: 'page_flow'
                },
                {
                  control: operation_list === 'banner',
                  key: 'operation_list',
                  value: 'banner'
                },
                {
                  control: operation_list === 'banner_right',
                  key: 'operation_list',
                  value: 'banner_right'
                },
                {
                  control: operation_list === 'author_latest_article',
                  key: 'operation_list',
                  value: 'author_latest_article'
                },
                {
                  control: operation_list === 'specialtopic_recommend',
                  key: 'operation_list',
                  value: 'specialtopic_recommend'
                },
                {
                  control: operation_list === 'video_recommend',
                  key: 'operation_list',
                  value: 'video_recommend'
                },
                {
                  control: operation_list === 'dailyword',
                  key: 'operation_list',
                  value: 'dailyword'
                },
                {
                  control: operation_list === 'latest_newsflash',
                  key: 'operation_list',
                  value: 'latest_newsflash'
                },
                {
                  control: operation_list === 'business_motif',
                  key: 'operation_list',
                  value: 'business_motif'
                },
                {
                  control: operation_list === 'hotlist',
                  key: 'operation_list',
                  value: 'hotlist'
                },
                {
                  control: operation_list === 'latest_videos',
                  key: 'operation_list',
                  value: 'latest_videos'
                },
                {
                  control: operation_list === 'recommend_motif',
                  key: 'operation_list',
                  value: 'recommend_motif'
                },
                {
                  control: operation_list === 'hot_author',
                  key: 'operation_list',
                  value: 'hot_author'
                },
                {
                  control: operation_list === 'next_card',
                  key: 'operation_list',
                  value: 'next_card'
                },
                {
                  control: index_num,
                  key: 'media_index_number',
                  value: index_num
                },
                {
                  control: operate_type,
                  key: 'operate_type',
                  value: operate_type
                },
                {
                  control: position_id,
                  key: 'position_id',
                  value: position_id
                },
                {
                  control: plan_id,
                  key: 'media_content_id',
                  value: plan_id
                },
                {
                  control: operate_type || position_id,
                  key: 'media_content_type',
                  value: 'ad'
                },
                {
                  control: position_id === '827' || operation_list === 'home_top',
                  key: 'operation_list',
                  value: 'home_top'
                },
                {
                  control: position_id === '817',
                  key: 'operation_list',
                  value: 'article_bottom'
                },
                {
                  control: position_id === '835',
                  key: 'operation_list',
                  value: 'comment_bottom'
                },
              ]
              const data = {};
              for (const infoItem of genPropertyAccordingToAttributeList) {
                Object.assign(data, setProperty(infoItem));
              }
              return data;
            }
            
            function manualMain ( elementTarget ) {
              // 只采集a标签的跳转
              if (elementTarget.tagName.toLowerCase() !== 'a') throw Error('目前只收集点击a标签所跳转的信息');
              const data = { platform: 'web' };
              const attributes = elementTarget.attributes;
              const hrefObj = attributes.href || {};
              const className = getClass(attributes);
              const hrefValue = hrefObj.value || "";
              if (/(http|ftp|https):///.test(hrefValue) && !hrefValue.includes('36kr.com')) {
                Object.assign(data, externalLink(hrefValue));
              } else {
                let internalLinkHrefValue = hrefValue;
                if (hrefValue.includes('36kr.com')) internalLinkHrefValue = hrefValue.split('36kr.com')[1];
                Object.assign(data, internalLink(internalLinkHrefValue, className));
              }
              // 通过标签属性，添加相关信息
              Object.assign(data, genPropertyAccordingToAttribute(elementTarget));
              return data;
            }
            
            function getClass(attributes) {
              const classObj = attributes.class || {};
              return classObj.value || "";
            }
            
            class PathInfo {
              constructor (path) {
                this.path = path
                const pathArr = path.split('/').filter(x => x !== "");
                this.root = pathArr[0];
                this.sub = pathArr[1];
              }
            }
            class PageInfo {
              constructor (href) {
                this.destPath = {
                  ...new PathInfo(href),
                  ...new SourcePath(href)
                }
                this.currentPath = {
                  ...new PathInfo(window.location.pathname),
                  ...new SourcePath(window.location.pathname)
                }
              }
            }
            
            class SourcePath {
              constructor (href) {
                this.group = SourcePath.groupFn(href);
              }
            
              static groupFn = href => {
                const pathInfo = new PathInfo(href);
                if (pathInfo.sub && isNumber(pathInfo.sub)) {
                  return 'content';
                } else {
                  return 'default';
                }
              }
            }
            
            function getCaseKey(data) {
              if(
                data.hasOwnProperty('operate_type') ||
                data.hasOwnProperty('position_id')
              ) {
                return 'MediaWebAD';
              } else {
                return 'MediaWebRead';
              }
            }
            
            function coverTagAClickEvent() {
              document.querySelectorAll('a').forEach(item => {
                item.addEventListener('click', function(e){
                  const elementTarget = item;
                  const targetValue = elementTarget.getAttribute('target');
                  const targetHref = elementTarget.getAttribute('href');
                  const url = this.href;
                  const data = manualMain(elementTarget);
                  if (!targetHref) return;
                  if (!targetValue) e.preventDefault();
                  window.sa.track(getCaseKey(data), data, function(){
                    if(!targetValue) location.href = url;
                  });
                });
              });
            }
            // 手动埋点内容
            window.onload = function() {
              setTimeout(() => {
                coverTagAClickEvent();
              },1200);
            }
          })();
        </script><script data-react-helmet="true" >
        // 神策统计代码
        (function(para) {
          var p = para.sdk_url, n = para.name, w = window, d = document, s = 'script',x = null,y = null;
          w['sensorsDataAnalytic201505'] = n;
          w[n] = w[n] || function(a) {return function() {(w[n]._q = w[n]._q || []).push([a, arguments]);}};
          var ifs = ['track','quick','register','registerPage','registerOnce','trackSignup', 'trackAbtest', 'setProfile','setOnceProfile','appendProfile', 'incrementProfile', 'deleteProfile', 'unsetProfile', 'identify','login','logout','trackLink','clearAllRegister','getAppStatus'];
          for (var i = 0; i < ifs.length; i++) {
            w[n][ifs[i]] = w[n].call(null, ifs[i]);
          }
          if (!w[n]._t) {
            x = d.createElement(s), y = d.getElementsByTagName(s)[0];
            x.async = 1;
            x.src = p;
            x.setAttribute('charset','UTF-8');
            y.parentNode.insertBefore(x, y);
            w[n].para = para;
          }
        })({
          sdk_url: 'https://static.sensorsdata.cn/sdk/1.16.5/sensorsdata.min.js',
          heatmap_url: 'https://static.sensorsdata.cn/sdk/1.16.5/heatmap.min.js',
          name: 'sa',
          web_url: 'https://data.corp.36kr.com/',
          server_url: 'https://36kr.com/global/sensors/sa/sa',
          heatmap:{
            //default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭。
            clickmap:'default',
            //default 表示开启，自动采集 $WebStay 事件，可以设置 'not_collect' 表示关闭。
            scroll_notice_map:'default',
          },
          source_type: {
            search: ['//baidu.com', 'cn.bing.com', '//sm.cn', 'quark.sm.cn'],
            social: ['.facebook.com'],
          },
          show_log: false,
        });
        (function(){
          const cookies = {};
          document.cookie.split('; ').forEach((c) => {
            const key = c.split('=')[0];
            const value = c.split('=')[1];
            cookies[key] = value;
          });
          const userid = cookies["userId"];
          const cid = cookies["kr_stat_uuid"];
          if (userid) {
            sa.login(userid);
          } else {
            sa.login(cid);
          }

          sa.quick('autoTrack');
        })();
      </script><script data-react-helmet="true" >
      
      /*解决微信设置字体大小改变布局*/
        (function() {
            if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
                handleFontSize();
            } else {
                if (document.addEventListener) {
                    document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
                } else if (document.attachEvent) {
                    document.attachEvent("WeixinJSBridgeReady", handleFontSize);
                    document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
                }
            }

            function handleFontSize() {
                // 设置网页字体为默认大小
                WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize' : 0 });
                // 重写设置网页字体大小的事件
                WeixinJSBridge.on('menu:setfont', function() {
                    WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize' : 0 });
                });
            }
        })();
      </script><script data-react-helmet="true" >
          // m站百度统计代码 添加时间:2022-1-19
          var _hmt = _hmt || [];
          (function() {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?320e0c9f02e06e5dc30ea88169fcacc5";
            var s = document.getElementsByTagName("script")[0]; 
            s.parentNode.insertBefore(hm, s);
          })();
        </script><script data-react-helmet="true" >
      // openinstall集成代码
      (function() {
        const s = document.createElement('script');
        s.type = 'text/javascript';
        s.src = 'https://web.cdn.openinstall.io/openinstall.js';
        s.addEventListener('load',()=>{
          // var data = OpenInstall.parseUrlParams();//openinstall.js中提供的工具函数，解析url中的所有查询参数
          window.op = new OpenInstall({
            /*appKey必选参数，平台为每个应用分配的ID*/
            appKey : "th9sia",
          }, {
            route: 'nav_recommend?subnavId=2'
          });
        },false)
        document.head.appendChild(s);
      })()
      </script><script data-react-helmet="true" >
        (function() {
          const ua = navigator.userAgent.toLowerCase();
          const isInApp = !!/36kr/i.test(ua);
          if (isInApp) {
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (e.matches) {
                // 系统开启暗色模式
                document.documentElement.setAttribute('data-theme', 'dark');
              } else {
                // 系统关闭暗色
                document.documentElement.setAttribute('data-theme', '');
              }
            });
          }
        })()
      </script><script data-react-helmet="true" src="https://lf1-cdn-tos.bytegoofy.com/goofy/developer/jssdk/jssdk-1.1.0.js"></script><script data-react-helmet="true" >
              !function(){
                function n(){
                  var n=(document.documentElement.clientWidth||window.innerWidth)/20;
                  // 在375设计稿到390设计稿的过渡中，同时兼容处理375和390的页面
                  if (false) {
                    document.documentElement.style.fontSize = n * (375/390) + "px";
                  } else {
                    document.documentElement.style.fontSize=n + "px";
                  }
                }
                /36kr/.test(navigator.userAgent)&&document.documentElement.classList.add("in-app");
                var inMiniProgram =  /miniprogram/i.test(navigator.userAgent);
                var inWechat = /micromessenger/i.test(navigator.userAgent);
                if (inWechat) {
                  if (inMiniProgram) {
                    document.documentElement.classList.add("in-miniprogram");
                  } else {
                    document.documentElement.classList.add("in-wechat");
                  }
                }
                window.addEventListener("resize",function(){
                  var e=document.activeElement;
                  null!==e&&e!==document.body&&void 0!==e||n()
                }),
                n()
              }();
            </script><script data-react-helmet="true" src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script><script data-react-helmet="true" >
      window.WEIXINSHARE = {
        shareTitle: "36氪_让一部分人先看到未来",
        shareImg: "https://img.36krcdn.com/20191024/v2_1571894049839_img_jpg",
        imgUrl: "https://img.36krcdn.com/20191024/v2_1571894049839_img_jpg",
        shareDesc: "36氪通过全面，独家的视角为用户深度剖析最前沿的资讯，致力于让一部分人先看到未来，内容涵盖快讯，科技，金融，投资，房产，汽车，互联网，股市，教育，生活，职场等，秉承着新商业媒体人的使命砥砺前行"
      }
    </script><script data-react-helmet="true" >
      (function () {
        var bp = document.createElement('script');
        var curProtocol = window.location.protocol.split(':')[0];
        if (curProtocol === 'https') {
          bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';
        }
        else {
          bp.src = 'http://push.zhanzhang.baidu.com/push.js';
        }
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(bp, s);
      })();
      </script><script data-react-helmet="true" >
        window.onload = function () {
          if (window.appletSa || sessionStorage.getItem('appletSa')) {
            sessionStorage.setItem('appletSa', window.appletSa || sessionStorage.getItem('appletSa'))
            sa.track('MediaPageVIew', {media_event_value: window.appletSa})
          }
        }
      </script>
</head>
<body>
<div id="app"><div class="goapp-wrapper"><div class="kr-mobile-layout"><div class="logo-container"><div class="logo"></div></div><div class="layout-children show-login"><div class="kr-mobile-home"><div class="home-entry-menu-wrap"><div class="home-banner"><div id="home-banner-swiper" class="swiper-container"><div class="swiper-wrapper"><div class="swiper-slide"><div class="item-wrapper"><div class="banner-item"><a class="item-pic" href="/p/3393680226601346" sensors_index_num="1" sensors_operation_list="banner" target="_blank" rel=""><img class="scaleBig" src="https://img.36krcdn.com/hsossms/20250725/v2_10b0cf0dea5f4c00ba3367f0b140915c@6221844_oswg828485oswg1053oswg495_img_png?x-oss-process=image/resize,m_mfit,w_960,h_400,limit_0/crop,w_960,h_400,g_center" alt="200美元键盘卖爆美日，这家深圳企业靠“反内卷设计”年收入过亿｜Insight全球"/></a><a class="item-title ellipsis-2 weight-bold" href="/p/3393680226601346" sensors_index_num="1" sensors_operation_list="banner" target="_blank" rel="">200美元键盘卖爆美日，这家深圳企业靠“反内卷设计”年收入过亿｜Insight全球</a></div></div></div><div class="swiper-slide"><div class="item-wrapper"><div class="banner-item"><a class="item-pic" href="/p/3393057116227714" sensors_index_num="2" sensors_operation_list="banner" target="_blank" rel=""><img class="scaleBig" src="https://img.36krcdn.com/hsossms/20250725/v2_99975df7251f4f21bf06dd8389684f30@5888275@ai_oswg764418oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:960:400:960:400:q70.jpg" alt="铂爵旅拍爆雷，李佳琦李诞加持的百亿市场凉了？"/></a><a class="item-title ellipsis-2 weight-bold" href="/p/3393057116227714" sensors_index_num="2" sensors_operation_list="banner" target="_blank" rel="">铂爵旅拍爆雷，李佳琦李诞加持的百亿市场凉了？</a></div></div></div><div class="swiper-slide"><div class="item-wrapper"><div class="banner-item"><a class="item-pic" href="https://myd.iscn.org.cn/#/s/8N7eFYUI?sourceId=706728" sensors_index_num="3" sensors_operation_list="banner" target="_blank" rel=""><img class="scaleBig" src="https://img.36krcdn.com/hsossms/20250722/v2_b804b9f5b2ed4eb19e7c50e7a7b2db3e@176070_oswg879508oswg1440oswg600_img_png?x-oss-process=image/resize,m_mfit,w_960,h_400,limit_0/crop,w_960,h_400,g_center" alt=""/></a></div></div></div><div class="swiper-slide"><div class="item-wrapper"><div class="home-ad-banner" data-id="823"><a class="ad-pic" sensors_operate_type="click" sensors_position_id="823" sensors_media_content_id="-BEFRbsrkScFSAmc2wSISWyp7sGEAXTEkXKPL6rhXtwMWnXK7cOJjyCBdeUIwEcGHfJpbUgpflfyGRvnMeNCoA" sensors_index_num="4" href="https://adx.36kr.com/api/ad/click?sign=4e78310471fe70d46849453d697d7ccb&amp;param.redirectUrl=aHR0cDovL2pzLnpqbWFkZS5jbjo4ODk5L3MvTjd2b3J6RmY&amp;param.adsdk=-BEFRbsrkScFSAmc2wSISWyp7sGEAXTEkXKPL6rhXtwMWnXK7cOJjyCBdeUIwEcGHfJpbUgpflfyGRvnMeNCoA" target="_blank" rel="noopener noreferrer"><span class="flag">广告</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="ad-title" href="https://adx.36kr.com/api/ad/click?sign=4e78310471fe70d46849453d697d7ccb&amp;param.redirectUrl=aHR0cDovL2pzLnpqbWFkZS5jbjo4ODk5L3MvTjd2b3J6RmY&amp;param.adsdk=-BEFRbsrkScFSAmc2wSISWyp7sGEAXTEkXKPL6rhXtwMWnXK7cOJjyCBdeUIwEcGHfJpbUgpflfyGRvnMeNCoA" sensors_operate_type="click" sensors_position_id="823" sensors_media_content_id="-BEFRbsrkScFSAmc2wSISWyp7sGEAXTEkXKPL6rhXtwMWnXK7cOJjyCBdeUIwEcGHfJpbUgpflfyGRvnMeNCoA" sensors_index_num="4" target="_blank" rel="noopener noreferrer">第四届全球数字贸易博览会“数贸创投日”西安站 | 项目方报名</a></div></div></div><div class="swiper-slide"><div class="item-wrapper"><div class="banner-item"><a class="item-pic" href="/p/3392986282477952" sensors_index_num="5" sensors_operation_list="banner" target="_blank" rel=""><img class="scaleBig" src="https://img.36krcdn.com/hsossms/20250725/v2_ff9d61fad030471cbeeb9267605fe0ec@5888275@ai_oswg867269oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:960:400:960:400:q70.jpg" alt="喜马拉雅要靠山，腾讯音乐要希望"/></a><a class="item-title ellipsis-2 weight-bold" href="/p/3392986282477952" sensors_index_num="5" sensors_operation_list="banner" target="_blank" rel="">喜马拉雅要靠山，腾讯音乐要希望</a></div></div></div></div><div class="swiper-pagination"></div></div></div><div class="home-fixed-area-wrap"><a class="home-fixed-area-item" href="/hot-list-m" target="_blank"><div class="home-fixed-area-item-box"><img class="home-fixed-area-item-icon" src="//static.36krcdn.com/36kr-mobile/static/<EMAIL>"/><div class="home-fixed-area-item-title">热榜</div></div></a><a class="home-fixed-area-item" href="/seek-report-new?isShowback=1" target="_blank"><div class="home-fixed-area-item-box"><img class="home-fixed-area-item-icon" src="//static.36krcdn.com/36kr-mobile/static/<EMAIL>"/><div class="home-fixed-area-item-title">寻求报道</div></div></a><a class="home-fixed-area-item" href="/live" target="_blank"><div class="home-fixed-area-item-box"><img class="home-fixed-area-item-icon" src="//static.36krcdn.com/36kr-mobile/static/<EMAIL>"/><div class="home-fixed-area-item-title">直播</div></div></a></div><div class="home-tab-bar-wrap"><div class="home-tab-bar-item active">文章</div><div class="home-tab-bar-item ">快讯</div><div class="home-tab-bar-item ">视频</div><div class="home-tab-bar-item ">动态</div></div></div><div class="kr-loading-more"><div class="home-flow"><div class="flow-list"><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393772228450694" target="_blank" rel=""><span class="item-feed">推荐</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393772228450694" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">开加盟店是赚还是赔？算清楚这三笔账就明白了</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">红餐网</span><span class="bar-time"><i class="icon"></i>9分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393749590120841" target="_blank" rel=""><span class="item-feed">消费</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393749590120841" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">为什么我说外卖大战是三输局？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">刀姐doris</span><span class="bar-time"><i class="icon"></i>11分钟前</span></div></a></div></div><div class="item-wrapper"><a href="https://adx.36kr.com/api/ad/click?sign=36ef2f73f0de60adfccd463bd4ff51ce&amp;param.redirectUrl=aHR0cHM6Ly8zNmtyLmNvbS9wLzMzOTEzNDY5ODk3NjI5NTI&amp;param.adsdk=MELwxlhEj2cf1AXBNaI6YDhh7kV_94jP2gFLAI1mlqlRYOA8y4aHjCmV5UjgleZzrCKDyRqxkxQ-OhYzL6YhiQ" sensors_operate_type="click" sensors_position_id="805" sensors_media_content_id="MELwxlhEj2cf1AXBNaI6YDhh7kV_94jP2gFLAI1mlqlRYOA8y4aHjCmV5UjgleZzrCKDyRqxkxQ-OhYzL6YhiQ" class="home-ad-flow-information" target="_blank" rel="noopener noreferrer nofollow" data-id="805"><div class="pic"><span class="flag">商业视角</span><div class="kr-default-img-no-logo "></div></div><div class="info"><div class="title weight-bold">从千店品牌到文化出海，星聚会KTV到底做对了什么</div></div></a></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393745440381316" target="_blank" rel=""><span class="item-feed">专精特新</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393745440381316" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">三闯IPO，北芯生命过会谁是赢家</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">投资者网</span><span class="bar-time"><i class="icon"></i>13分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393745192683648" target="_blank" rel=""><span class="item-feed">推荐</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393745192683648" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">“很多朋友出海，转来转去都是中国人”</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">36氪的朋友们</span><span class="bar-time"><i class="icon"></i>14分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393744947939456" target="_blank" rel=""><span class="item-feed">推荐</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393744947939456" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">AI创业半年被5.7亿收购，31岁CEO带队8人瓜分亿元现金红包，此前没融1分钱</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">量子位</span><span class="bar-time"><i class="icon"></i>17分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393735012157824" target="_blank" rel=""><span class="item-feed">科技</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393735012157824" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">颠覆通用CPU，全球最省电处理器，正式发布</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">半导体行业观察</span><span class="bar-time"><i class="icon"></i>17分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393734901995654" target="_blank" rel=""><span class="item-feed">推荐</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393734901995654" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">辛选否认卫生巾含致癌物，辛巴和“家人们”谁在“撒谎”？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">趣解商业</span><span class="bar-time"><i class="icon"></i>21分钟前</span></div></a></div></div><div class="item-wrapper"><a href="https://adx.36kr.com/api/ad/click?sign=e0532e35082f26b02c70a07b5c5e71a3&amp;param.redirectUrl=aHR0cDovL2pzLnpqbWFkZS5jbjo4ODk5L3MvWEdNRmlUbHg&amp;param.adsdk=QQSJKKuFg0Sme7YcAbAKvBT_zanYfI95PeJ7-x5dG1A7kiof7x058DATaNxxS_CT_wlzw31vA5ZtNmfzmdOAdg" sensors_operate_type="click" sensors_position_id="806" sensors_media_content_id="QQSJKKuFg0Sme7YcAbAKvBT_zanYfI95PeJ7-x5dG1A7kiof7x058DATaNxxS_CT_wlzw31vA5ZtNmfzmdOAdg" class="home-ad-flow-information" target="_blank" rel="noopener noreferrer nofollow" data-id="806"><div class="pic"><span class="flag">商业视角</span><div class="kr-default-img-no-logo "></div></div><div class="info"><div class="title weight-bold">第四届全球数字贸易博览会“数贸创投日”西安站 | 资本方报名</div></div></a></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393698390874499" target="_blank" rel=""><span class="item-feed">财经</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393698390874499" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">从房主任爆火说起：精英们渴望被冒犯</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">着陆TouchBase</span><span class="bar-time"><i class="icon"></i>46分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393697529465224" target="_blank" rel=""><span class="item-feed">财经</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393697529465224" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">“央妈”什么情况下发行纪念币？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">36氪的朋友们</span><span class="bar-time"><i class="icon"></i>48分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393694921218440" target="_blank" rel=""><span class="item-feed">财经</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393694921218440" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">英特尔盘后跳水，营收超预期，却藏致命隐忧，AMD笑到最后？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">美股投资网</span><span class="bar-time"><i class="icon"></i>49分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393692145518985" target="_blank" rel=""><span class="item-feed">推荐</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393692145518985" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">iOS 26 公测版上手体验：iPhone 系统未来十年长这样？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">爱范儿</span><span class="bar-time"><i class="icon"></i>51分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393682873944199" target="_blank" rel=""><span class="item-feed">财经</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393682873944199" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">短剧“霸主”红果，革了谁的命？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">新眸</span><span class="bar-time"><i class="icon"></i>52分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393618198890889" target="_blank" rel=""><span class="item-feed">财经</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393618198890889" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">天域半导体再闯IPO</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">36氪的朋友们</span><span class="bar-time"><i class="icon"></i>56分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393680585824647" target="_blank" rel=""><span class="item-feed">科技</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393680585824647" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">新势力们下一个主线任务：卖到100万辆，其他都是噪音</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">节点财经</span><span class="bar-time"><i class="icon"></i>59分钟前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393756609857922" target="_blank" rel=""><span class="item-feed">城市</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393756609857922" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">上半年50+重磅级高管变动，2025商业地产企业都在“大手笔”抢人！</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">赢商网</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393601586727042" target="_blank" rel=""><span class="item-feed">财经</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393601586727042" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">坐拥三千亿市场：网文改编，鹿死谁手？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">文娱先声</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393617986505097" target="_blank" rel=""><span class="item-feed">财经</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393617986505097" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">新消费有多猛？刘格菘把泡泡玛特买到第二大重仓</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">36氪的朋友们</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393600438897032" target="_blank" rel=""><span class="item-feed">财经</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393600438897032" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">沃森生物，终于有人做主了？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">医曜</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393257428732037" target="_blank" rel=""><span class="item-feed">科技</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393257428732037" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">爆料，GPT-5 即将8月发布，人类历史上最强AI模型</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">美股投资网</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393753553668480" target="_blank" rel=""><span class="item-feed">城市</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393753553668480" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">曙光微现，“出险”房企的涅槃重生之路</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">未来城不落</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393010045077634" target="_blank" rel=""><span class="item-feed">科技</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393010045077634" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">AI透镜系列研究：AI Coding非共识报告</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">腾讯研究院</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393748470433926" target="_blank" rel=""><span class="item-feed">城市</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393748470433926" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">浙江汽车模具大王，上海争夺地王？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">路数</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393003948460416" target="_blank" rel=""><span class="item-feed">财经</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393003948460416" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">涨12.2%，皮肤科巨头今年有望冲击十强？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">聚美丽</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3392977112160393" target="_blank" rel=""><span class="item-feed">科技</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3392977112160393" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">小鹏校准 SUV 产品线，G6 摸索降本、降价方案</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">汽车像素</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3392944797882752" target="_blank" rel=""><span class="item-feed">科技</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3392944797882752" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">奥迪A4换代预售价历史新低，但直播间都让把车推沟里去</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">源媒汇</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div><div class="item-wrapper"><div class="article-item"><a class="item-pic " href="/p/3393741305202821" target="_blank" rel=""><span class="item-feed">城市</span><div class="kr-default-img-no-logo scaleBig"></div></a><a class="item-info clearfloat" href="/p/3393741305202821" target="_blank" rel="noopener noreferrer"><span class="item-title weight-bold ellipsis-2">哪一种物业付费模式才是未来？</span><div class="item-bar"><span class="bar-author" rel="noopener noreferrer">丁祖昱评楼市</span><span class="bar-time"><i class="icon"></i>1小时前</span></div></a></div></div></div></div><div style="height:0;width:100%;visibility:hidden"></div><div class="kr-loading-more-button-default">查看更多</div></div><div class="kr-white-space" style="height:25px;width:100%;visibility:hidden"></div></div></div></div></div></div>
<script>window.initialState={"navigator":{"userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1"},"theme":"default","isSpider":false,"home":{"banner":[{"itemId":3393680226601346,"itemType":10,"templateMaterial":{"itemId":3393680226601346,"templateType":0,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_10b0cf0dea5f4c00ba3367f0b140915c@6221844_oswg828485oswg1053oswg495_img_png?x-oss-process=image/resize,m_mfit,w_960,h_400,limit_0/crop,w_960,h_400,g_center","widgetTitle":"200美元键盘卖爆美日，这家深圳企业靠“反内卷设计”年收入过亿｜Insight全球","publishTime":1753408238544},"route":"detail_article?itemId=3393680226601346","publishTime":1753408238544},{"itemId":3393057116227714,"itemType":10,"templateMaterial":{"itemId":3393057116227714,"templateType":0,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_99975df7251f4f21bf06dd8389684f30@5888275@ai_oswg764418oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:960:400:960:400:q70.jpg","widgetTitle":"铂爵旅拍爆雷，李佳琦李诞加持的百亿市场凉了？","publishTime":1753404053157},"route":"detail_article?itemId=3393057116227714","publishTime":1753404053157},{"itemId":0,"itemType":1,"templateMaterial":{"itemId":0,"templateType":0,"widgetImage":"https://img.36krcdn.com/hsossms/20250722/v2_b804b9f5b2ed4eb19e7c50e7a7b2db3e@176070_oswg879508oswg1440oswg600_img_png?x-oss-process=image/resize,m_mfit,w_960,h_400,limit_0/crop,w_960,h_400,g_center","widgetTitle":"","publishTime":1753153589844},"route":"webview?url=https%3A%2F%2Fmyd.iscn.org.cn%2F%23%2Fs%2F8N7eFYUI%3FsourceId%3D706728","publishTime":1753153589844},{"itemId":23309,"itemType":0,"templateMaterial":{"positionId":823,"planId":23309,"adSdk":"-BEFRbsrkScFSAmc2wSISWyp7sGEAXTEkXKPL6rhXtwMWnXK7cOJjyCBdeUIwEcGHfJpbUgpflfyGRvnMeNCoA","adExposureUrl":"https://adx.36kr.com/api/ad/exposure?sign=34dc51a4be5993d9bf4da84d3e3ee8f0&param.adsdk=-BEFRbsrkScFSAmc2wSISWyp7sGEAXTEkXKPL6rhXtwMWnXK7cOJjyCBdeUIwEcGHfJpbUgpflfyGRvnMeNCoA","adJsonContent":"{\"title\":\"第四届全球数字贸易博览会“数贸创投日”西安站 | 项目方报名\",\"src\":\"https://img.36krcdn.com/hsossms/20250723/v2_d9405571ca49404db526f50918cef8a2@000000_oswg1484878oswg1440oswg600_img_png\",\"href\":\"https://adx.36kr.com/api/ad/click?sign=4e78310471fe70d46849453d697d7ccb&param.redirectUrl=aHR0cDovL2pzLnpqbWFkZS5jbjo4ODk5L3MvTjd2b3J6RmY&param.adsdk=-BEFRbsrkScFSAmc2wSISWyp7sGEAXTEkXKPL6rhXtwMWnXK7cOJjyCBdeUIwEcGHfJpbUgpflfyGRvnMeNCoA\"}","flag":"广告","planType":1},"publishTime":0},{"itemId":3392986282477952,"itemType":10,"templateMaterial":{"itemId":3392986282477952,"templateType":0,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_ff9d61fad030471cbeeb9267605fe0ec@5888275@ai_oswg867269oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:960:400:960:400:q70.jpg","widgetTitle":"喜马拉雅要靠山，腾讯音乐要希望","publishTime":1753404043238},"route":"detail_article?itemId=3392986282477952","publishTime":1753404043238}],"flow":{"itemList":[{"itemId":3393772228450694,"itemType":10,"templateMaterial":{"itemId":3393772228450694,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_94dea6dded754f2d913ab1c97d98cdef@5888275@ai_oswg936590oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"开加盟店是赚还是赔？算清楚这三笔账就明白了","publishTime":1753413928034,"authorName":"红餐网","navName":"推荐"},"route":"detail_article?itemId=3393772228450694","siteId":1,"publishTime":1753413928034},{"itemId":3393749590120841,"itemType":10,"templateMaterial":{"itemId":3393749590120841,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_50ee2f3c98a54b90b49e2d82bead4994@5888275@ai_oswg779821oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"为什么我说外卖大战是三输局？","publishTime":1753413815268,"authorName":"刀姐doris","navName":"消费"},"route":"detail_article?itemId=3393749590120841","siteId":1,"publishTime":1753413815268},{"itemId":23230,"itemType":0,"templateMaterial":{"positionId":805,"planId":23230,"adSdk":"MELwxlhEj2cf1AXBNaI6YDhh7kV_94jP2gFLAI1mlqlRYOA8y4aHjCmV5UjgleZzrCKDyRqxkxQ-OhYzL6YhiQ","adExposureUrl":"https://adx.36kr.com/api/ad/exposure?sign=f823c6ee82d2ff0b68affc5761c3dc18&param.adsdk=MELwxlhEj2cf1AXBNaI6YDhh7kV_94jP2gFLAI1mlqlRYOA8y4aHjCmV5UjgleZzrCKDyRqxkxQ-OhYzL6YhiQ","adJsonContent":"{\"description\":\"从千店品牌到文化出海，星聚会KTV到底做对了什么\",\"href\":\"https://adx.36kr.com/api/ad/click?sign=36ef2f73f0de60adfccd463bd4ff51ce&param.redirectUrl=aHR0cHM6Ly8zNmtyLmNvbS9wLzMzOTEzNDY5ODk3NjI5NTI&param.adsdk=MELwxlhEj2cf1AXBNaI6YDhh7kV_94jP2gFLAI1mlqlRYOA8y4aHjCmV5UjgleZzrCKDyRqxkxQ-OhYzL6YhiQ\",\"title\":\"从千店品牌到文化出海，星聚会KTV到底做对了什么\",\"src\":\"https://img.36krcdn.com/hsossms/20250724/v2_8ad45480db3940f4abdefb90d1062702@000000_img_gif\"}","flag":"商业视角","planType":1},"publishTime":0},{"itemId":3393745440381316,"itemType":10,"templateMaterial":{"itemId":3393745440381316,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_6ce0fe06bb024ee597a99d7500b9e178@5888275@ai_oswg895025oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"三闯IPO，北芯生命过会谁是赢家","publishTime":1753413711859,"authorName":"投资者网","navName":"专精特新"},"route":"detail_article?itemId=3393745440381316","siteId":1,"publishTime":1753413711859},{"itemId":3393745192683648,"itemType":10,"templateMaterial":{"itemId":3393745192683648,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_57a2946399ac42198b13b5ddded27404@000000@ai_oswg369644oswg1536oswg722_img_000~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"“很多朋友出海，转来转去都是中国人”","publishTime":1753413628095,"authorName":"36氪的朋友们","navName":"推荐"},"route":"detail_article?itemId=3393745192683648","siteId":1,"publishTime":1753413628095},{"itemId":3393744947939456,"itemType":10,"templateMaterial":{"itemId":3393744947939456,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_a801d9ccf47f433b980f55cc50c4cbeb@000000@ai_oswg334190oswg1536oswg722_img_000~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"AI创业半年被5.7亿收购，31岁CEO带队8人瓜分亿元现金红包，此前没融1分钱","publishTime":1753413480273,"authorName":"量子位","navName":"推荐"},"route":"detail_article?itemId=3393744947939456","siteId":1,"publishTime":1753413480273},{"itemId":3393735012157824,"itemType":10,"templateMaterial":{"itemId":3393735012157824,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_f4ba80bea8f247f283b67e0a732c825c@5888275@ai_oswg897909oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"颠覆通用CPU，全球最省电处理器，正式发布","publishTime":1753413439273,"authorName":"半导体行业观察","navName":"科技"},"route":"detail_article?itemId=3393735012157824","siteId":1,"publishTime":1753413439273},{"itemId":3393734901995654,"itemType":10,"templateMaterial":{"itemId":3393734901995654,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_1c7dbe4e245e41848bbd436b8b695075@5888275@ai_oswg787834oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"辛选否认卫生巾含致癌物，辛巴和“家人们”谁在“撒谎”？","publishTime":1753413233066,"authorName":"趣解商业","navName":"推荐"},"route":"detail_article?itemId=3393734901995654","siteId":1,"publishTime":1753413233066},{"itemId":23304,"itemType":0,"templateMaterial":{"positionId":806,"planId":23304,"adSdk":"QQSJKKuFg0Sme7YcAbAKvBT_zanYfI95PeJ7-x5dG1A7kiof7x058DATaNxxS_CT_wlzw31vA5ZtNmfzmdOAdg","adExposureUrl":"https://adx.36kr.com/api/ad/exposure?sign=d68cb106a985c1cca5130591359ceed6&param.adsdk=QQSJKKuFg0Sme7YcAbAKvBT_zanYfI95PeJ7-x5dG1A7kiof7x058DATaNxxS_CT_wlzw31vA5ZtNmfzmdOAdg","adJsonContent":"{\"description\":\"本场活动聚焦空天信息、人工智能、智慧物流、航空航天等黄金赛道。促进数字贸易领域科技创业项目与资本的高效对接。\",\"href\":\"https://adx.36kr.com/api/ad/click?sign=e0532e35082f26b02c70a07b5c5e71a3&param.redirectUrl=aHR0cDovL2pzLnpqbWFkZS5jbjo4ODk5L3MvWEdNRmlUbHg&param.adsdk=QQSJKKuFg0Sme7YcAbAKvBT_zanYfI95PeJ7-x5dG1A7kiof7x058DATaNxxS_CT_wlzw31vA5ZtNmfzmdOAdg\",\"title\":\"第四届全球数字贸易博览会“数贸创投日”西安站 | 资本方报名\",\"src\":\"https://img.36krcdn.com/hsossms/20250722/v2_a6d0595307f543f4987a45ed37b54bcf@000000_oswg273952oswg432oswg288_img_jpg\"}","flag":"商业视角","planType":1},"publishTime":0},{"itemId":3393698390874499,"itemType":10,"templateMaterial":{"itemId":3393698390874499,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_1f405559e4464811b7441313bc379a65@5888275@ai_oswg965215oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"从房主任爆火说起：精英们渴望被冒犯","publishTime":1753411735928,"authorName":"着陆TouchBase","navName":"财经"},"route":"detail_article?itemId=3393698390874499","siteId":1,"publishTime":1753411735928},{"itemId":3393697529465224,"itemType":10,"templateMaterial":{"itemId":3393697529465224,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_ab9dccad78864bc7a17fb16a07b0513c@000000@ai_oswg418346oswg1536oswg722_img_000~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"“央妈”什么情况下发行纪念币？","publishTime":1753411600764,"authorName":"36氪的朋友们","navName":"财经"},"route":"detail_article?itemId=3393697529465224","siteId":1,"publishTime":1753411600764},{"itemId":3393694921218440,"itemType":10,"templateMaterial":{"itemId":3393694921218440,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_b49f07bfccf94735863bbd17fc5ae005@5888275@ai_oswg806984oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"英特尔盘后跳水，营收超预期，却藏致命隐忧，AMD笑到最后？","publishTime":1753411563030,"authorName":"美股投资网","navName":"财经"},"route":"detail_article?itemId=3393694921218440","siteId":1,"publishTime":1753411563030},{"itemId":3393692145518985,"itemType":10,"templateMaterial":{"itemId":3393692145518985,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_7587820090af419da53f22f8bf7728d6@6100851_oswg649582oswg1053oswg495_img_png?x-oss-process=image/resize,m_mfit,w_600,h_400,limit_0/crop,w_600,h_400,g_center","widgetTitle":"iOS 26 公测版上手体验：iPhone 系统未来十年长这样？","publishTime":1753411403161,"authorName":"爱范儿","navName":"推荐"},"route":"detail_article?itemId=3393692145518985","siteId":1,"publishTime":1753411403161},{"itemId":3393682873944199,"itemType":10,"templateMaterial":{"itemId":3393682873944199,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_36607d7dd2f343e181f5e09245d736c7@000000@ai_oswg342900oswg1536oswg722_img_000~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"短剧“霸主”红果，革了谁的命？","publishTime":1753411350329,"authorName":"新眸","navName":"财经"},"route":"detail_article?itemId=3393682873944199","siteId":1,"publishTime":1753411350329},{"itemId":3393618198890889,"itemType":10,"templateMaterial":{"itemId":3393618198890889,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_5c7c7b69bd3249e4aaa7f8bd0b39a05f@5888275@ai_oswg864720oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"天域半导体再闯IPO","publishTime":1753411099613,"authorName":"36氪的朋友们","navName":"财经"},"route":"detail_article?itemId=3393618198890889","siteId":1,"publishTime":1753411099613},{"itemId":3393680585824647,"itemType":10,"templateMaterial":{"itemId":3393680585824647,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_4ab6bd597c93491083d658e863e3e38e@5888275@ai_oswg856429oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"新势力们下一个主线任务：卖到100万辆，其他都是噪音","publishTime":1753410909788,"authorName":"节点财经","navName":"科技"},"route":"detail_article?itemId=3393680585824647","siteId":1,"publishTime":1753410909788},{"itemId":3393756609857922,"itemType":10,"templateMaterial":{"itemId":3393756609857922,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_e7fa92e0e998465ebea36aeda8bf8e84@6208419_oswg776943oswg1053oswg495_img_png?x-oss-process=image/resize,m_mfit,w_600,h_400,limit_0/crop,w_600,h_400,g_center","widgetTitle":"上半年50+重磅级高管变动，2025商业地产企业都在“大手笔”抢人！","publishTime":1753410806579,"authorName":"赢商网","navName":"城市"},"route":"detail_article?itemId=3393756609857922","siteId":1,"publishTime":1753410806579},{"itemId":3393601586727042,"itemType":10,"templateMaterial":{"itemId":3393601586727042,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_44051ba305f548578548b9e5ea3e9fac@5888275@ai_oswg992431oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"坐拥三千亿市场：网文改编，鹿死谁手？","publishTime":1753410776934,"authorName":"文娱先声","navName":"财经"},"route":"detail_article?itemId=3393601586727042","siteId":1,"publishTime":1753410776934},{"itemId":3393617986505097,"itemType":10,"templateMaterial":{"itemId":3393617986505097,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_2093006db07c42f4ae5df1345e6ed55b@5888275@ai_oswg940488oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"新消费有多猛？刘格菘把泡泡玛特买到第二大重仓","publishTime":1753410646940,"authorName":"36氪的朋友们","navName":"财经"},"route":"detail_article?itemId=3393617986505097","siteId":1,"publishTime":1753410646940},{"itemId":3393600438897032,"itemType":10,"templateMaterial":{"itemId":3393600438897032,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_7911f664cdd24cddb988feaae73314bb@5888275@ai_oswg825092oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"沃森生物，终于有人做主了？","publishTime":1753410585612,"authorName":"医曜","navName":"财经"},"route":"detail_article?itemId=3393600438897032","siteId":1,"publishTime":1753410585612},{"itemId":3393257428732037,"itemType":10,"templateMaterial":{"itemId":3393257428732037,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_244761b9fe7b416c8cbb2a0815e24475@000000@ai_oswg248383oswg1536oswg722_img_000~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"爆料，GPT-5 即将8月发布，人类历史上最强AI模型","publishTime":1753410425092,"authorName":"美股投资网","navName":"科技"},"route":"detail_article?itemId=3393257428732037","siteId":1,"publishTime":1753410425092},{"itemId":3393753553668480,"itemType":10,"templateMaterial":{"itemId":3393753553668480,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_2f859d4c5a5049939182da9f27ff0dde@6208419_oswg595257oswg1053oswg495_img_png?x-oss-process=image/resize,m_mfit,w_600,h_400,limit_0/crop,w_600,h_400,g_center","widgetTitle":"曙光微现，“出险”房企的涅槃重生之路","publishTime":1753410405912,"authorName":"未来城不落","navName":"城市"},"route":"detail_article?itemId=3393753553668480","siteId":1,"publishTime":1753410405912},{"itemId":3393010045077634,"itemType":10,"templateMaterial":{"itemId":3393010045077634,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250724/v2_18e675fd7ad5412496be9c83f96c4002@000000@ai_oswg272481oswg1536oswg722_img_000~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"AI透镜系列研究：AI Coding非共识报告","publishTime":1753410241921,"authorName":"腾讯研究院","navName":"科技"},"route":"detail_article?itemId=3393010045077634","siteId":1,"publishTime":1753410241921},{"itemId":3393748470433926,"itemType":10,"templateMaterial":{"itemId":3393748470433926,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_d7ab393894fe424d88c2538628a1ea57@6208419@ai_oswg903461oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"浙江汽车模具大王，上海争夺地王？","publishTime":1753410239216,"authorName":"路数","navName":"城市"},"route":"detail_article?itemId=3393748470433926","siteId":1,"publishTime":1753410239216},{"itemId":3393003948460416,"itemType":10,"templateMaterial":{"itemId":3393003948460416,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250724/v2_39a5c829115747c683abb6aaed3181f6@000000@ai_oswg315222oswg1536oswg722_img_000~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"涨12.2%，皮肤科巨头今年有望冲击十强？","publishTime":1753410160443,"authorName":"聚美丽","navName":"财经"},"route":"detail_article?itemId=3393003948460416","siteId":1,"publishTime":1753410160443},{"itemId":3392977112160393,"itemType":10,"templateMaterial":{"itemId":3392977112160393,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_f99537e37f2147a5be2d2917a72cca26@5888275@ai_oswg782620oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"小鹏校准 SUV 产品线，G6 摸索降本、降价方案","publishTime":1753410064301,"authorName":"汽车像素","navName":"科技"},"route":"detail_article?itemId=3392977112160393","siteId":1,"publishTime":1753410064301},{"itemId":3392944797882752,"itemType":10,"templateMaterial":{"itemId":3392944797882752,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_b52093a39cdc40e8ab16a1778e8520f1@5888275@ai_oswg799772oswg1053oswg495_img_png~tplv-1marlgjv7f-ai-v3:600:400:600:400:q70.jpg","widgetTitle":"奥迪A4换代预售价历史新低，但直播间都让把车推沟里去","publishTime":1753409938899,"authorName":"源媒汇","navName":"科技"},"route":"detail_article?itemId=3392944797882752","siteId":1,"publishTime":1753409938899},{"itemId":3393741305202821,"itemType":10,"templateMaterial":{"itemId":3393741305202821,"templateType":1,"widgetImage":"https://img.36krcdn.com/hsossms/20250725/v2_77f27f1fa85e4193a3732c3a3e5c8cb7@6208419_oswg606398oswg1053oswg495_img_png?x-oss-process=image/resize,m_mfit,w_600,h_400,limit_0/crop,w_600,h_400,g_center","widgetTitle":"哪一种物业付费模式才是未来？","publishTime":1753409823751,"authorName":"丁祖昱评楼市","navName":"城市"},"route":"detail_article?itemId=3393741305202821","siteId":1,"publishTime":1753409823751}],"pageCallback":"eyJmaXJzdElkIjo1MDAzNDc4LCJsYXN0SWQiOjUwMDMzNDUsImZpcnN0Q3JlYXRlVGltZSI6MTc1MzQxMzkyODAzNCwibGFzdENyZWF0ZVRpbWUiOjE3NTM0MDk4MjM3NTF9","hasNextPage":1},"skinConfig":{"startTime":"2022/01/31 00:00:00","endTime":"2022/02/08 00:00:00"},"apolloConfigDataDetail":{"redis_exitTime_oneDay":"172800","redis_exitTime_oneWeek":"172800","redis_exitTime_oneMonth":"172800","redis_exitTime_threeMonth":"172800","redis_exitTime_longest":"216000","skinStartTime":"2025/03/03 14:00:00","skinEndTime":"2025/03/12 23:59:59","isOpenSkin":"1","redis_exitTime_sixMonth":"172800","filterArticleUrlTime":"2022/01/01 00:00:00","isOpenRedis":"1","apolloHostWhiteListString":"http://www.36kr.com,www.36dianping.com,mp.weixin.qq.com,www.people.com.cn,politics.people.com.cn/,xinhuanet.com,www.cctv.com,www.china.com.cn,www.cri.cn,cn.chinadaily.com.cn,www.youth.cn,www.ce.cn,www.taiwan.cn,www.tibet.cn,www.gmw.cn,www.cnr.cn,81.cn,www.chinanews.com.cn,www.cppcc.gov.cn,www.legaldaily.com.cn,www.cyol.net,www.huanqiu.com,www.workercn.cn,www.k618.cn,www.haiwainet.cn,www.jcrb.com,www.chinaso.com,dangjian.people.com.cn,www.qstheory.cn,www.theorychina.org.cn,bbs1.people.com.cn,www.gongwei.org.cn,www.wenming.cn,www.xuexi.cn,cnwomen.com.cn,www.crnews.net,www.chinaqw.com,www.chinadevelopment.com.cn,cankaoxiaoxi.com,www.cnenergynews.cn,www.ceweekly.cn,www.globalpeople.com.cn,www.banyuetan.org,jjckb.xinhuanet.com,news.chinaxiaokang.com,www.zqrb.cn,news.eol.cn,www.chisa.edu.cn,www.stdaily.com,www.cnii.com.cn,mzb.com.cn,www.chinacourt.org,www.chinacngn.cn,fecn.net,www.zuzhirenshi.com,www.clssn.com,www.cenews.com.cn,www.zgjscmw.com,www.zgjtb.com,www.zgsyb.com,www.chinawater.com.cn,sannongfabu.ntv.cn,ctnews.com.cn,www.jkb.com.cn,aqsc.cn,www.zgswcn.com,www.xfrb.com.cn,www.ctaxnews.com.cn,www.cqn.com.cn,www.ccn.com.cn,www.cmrnn.com.cn,cb.com.cn,www.ce.cn,www.zgqxb.com.cn,www.cbimc.cn,xtg.cpnn.com.cn,www.greenchina.tv,www.cpd.com.cn,bbs.peoplerail.com,tv.81.cn,www.huaxia.com,www.letschuhai.com,www.mikecrm.com","offsetNumber":"1","redis_exitTime_common_v1":"86400","article_detail_redis_version":"v6","page_project_all_data_version":"v10","https://gateway-pre.36kr.com/api/mis/page/article":"{\"idName\":\"itemId\",\"version\":\"v6\",\"baseExitTime\":86400,\"offsetNumber\":\"1\",\"isOpenRedis\":true,\"isArticle\":true,\"filterArticleUrlTime\":\"2022/01/01 00:00:00\"}","https://gateway-pre.36kr.com/api/seo/tag/newsflash/relation":"{\"idName\":\"id\",\"version\":\"v1\",\"baseExitTime\":86400,\"offsetNumber\":1,\"isOpenRedis\":true}","https://gateway-pre.36kr.com/api/seo/tag/newsflash/recom":"{\"version\": \"v1\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway-pre.36kr.com/api/seo/tag/article/recom":"{\"version\": \"v1\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway-pre.36kr.com/api/seo/tag/article/flow":"{\"idName\": \"tagId\",\"version\": \"v1\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway-pre.36kr.com/api/seo/tag/article/hotTag":"{\"version\": \"v1\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway-pre.36kr.com/api/seo/tag/newsflash/hotTag":"{\"version\": \"v1\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway-pre.36kr.com/api/seo/tag/newsflash/flow":"{\"idName\": \"tagId\",\"version\": \"v1\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway-pre.36kr.com/api/seo/tag/article/relation":"{\"idName\": \"id\",\"version\": \"v1\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway-pre.36kr.com/api/mis/page/project":"{\"idName\": \"projectId\",\"version\": \"v7\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","urlForbidSsrArr":"/project/:id,/search/articles/:id","https://gateway.36kr.com/api/mis/page/article":"{\"idName\":\"itemId\",\"version\":\"v16\",\"baseExitTime\":864000,\"offsetNumber\":1,\"isOpenRedis\":true,\"isArticle\":true,\"filterArticleUrlTime\":\"2022/01/01 00:00:00\"}","https://gateway.36kr.com/api/seo/tag/newsflash/relation":"{\"idName\":\"id\",\"version\":\"v15\",\"baseExitTime\":86400,\"offsetNumber\":1,\"isOpenRedis\":true}","https://gateway.36kr.com/api/seo/tag/newsflash/recom":"{\"version\": \"v15\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway.36kr.com/api/seo/tag/article/recom":"{\"version\": \"v15\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway.36kr.com/api/seo/tag/article/flow":"{\"idName\": \"tagId\",\"version\": \"v17\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway.36kr.com/api/seo/tag/article/hotTag":"{\"version\": \"v15\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway.36kr.com/api/seo/tag/newsflash/hotTag":"{\"version\": \"v15\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway.36kr.com/api/seo/tag/newsflash/flow":"{\"idName\": \"tagId\",\"version\": \"v16\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway.36kr.com/api/seo/tag/article/relation":"{\"idName\": \"id\",\"version\": \"v15\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","https://gateway.36kr.com/api/mis/page/project":"{\"idName\": \"projectId\",\"version\": \"v16\",\"baseExitTime\": 86400,\"offsetNumber\": 1,\"isOpenRedis\": true}","WXMessageType":"miniprogram","isOpenGreySkin":"0","skinBannerPC":"https://img.36krcdn.com/hsossms/20250228/v2_022f2404f67d49e68def6b612387863f@000000_oswg143752oswg2120oswg320_img_png","skinBannerH5":"https://img.36krcdn.com/hsossms/20250228/v2_17d550bb9ad0413391f6392add075bc0@000000_oswg110368oswg1170oswg624_img_png"}}}</script>
<script src="//static.36krcdn.com/36kr-mobile/static/runtime.7d47408d.js" type="text/javascript"></script><script src="//static.36krcdn.com/36kr-mobile/static/app.53ef2c6b.js" type="text/javascript"></script>
</body>
</html>