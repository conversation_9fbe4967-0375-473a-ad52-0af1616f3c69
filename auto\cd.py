from DrissionPage import ChromiumPage, ChromiumOptions
import time

if __name__ == '__main__':

    # co = ChromiumOptions()
    # # 设置全屏
    # co.set_argument('--start-maximized')

    # page = ChromiumPage(co)

    # # page.get('https://www.changdunovel.com/sale/monitor/center')
    # # time.sleep(1)

    # time.sleep(30)

    # page.quit()
    text = "_ga=GA1.1.*********.1744855045; _ga_RK93FEJP22=GS1.1.1744855044.1.1.1744855461.0.0.0; passport_csrf_token=0e9d31c8f6bb91a47a162dffdc9aef36; passport_csrf_token_default=0e9d31c8f6bb91a47a162dffdc9aef36; n_mh=9-mIeuD4wZnlYrrOvfzG3MuT6aQmCUtmr8FxV8Kl8xY; is_staff_user=false; sid_guard=633978535b8a83e763a8409b738727e9%7C1754983352%7C5184000%7CSat%2C+11-Oct-2025+07%3A22%3A32+GMT; uid_tt=8cae6205c717f5656e4384eb43aeb238; uid_tt_ss=8cae6205c717f5656e4384eb43aeb238; sid_tt=633978535b8a83e763a8409b738727e9; sessionid=633978535b8a83e763a8409b738727e9; sessionid_ss=633978535b8a83e763a8409b738727e9; session_tlb_tag=sttt%7C12%7CYzl4U1uKg-djqECbc4cn6f_________qmdvdD1bsbp65V8irWj320kEAUSqPOvoSrdqfx6mUDyo%3D; sid_ucp_v1=1.0.0-KGE4YzRkZjI4MGNkNGFhMWE4NzA5ODQzYWFlZTRjNzNiOWRkNTUzYjAKFwis0pCl3MzCBxC41-vEBhimDDgBQOoHGgJobCIgNjMzOTc4NTM1YjhhODNlNzYzYTg0MDliNzM4NzI3ZTk; ssid_ucp_v1=1.0.0-KGE4YzRkZjI4MGNkNGFhMWE4NzA5ODQzYWFlZTRjNzNiOWRkNTUzYjAKFwis0pCl3MzCBxC41-vEBhimDDgBQOoHGgJobCIgNjMzOTc4NTM1YjhhODNlNzYzYTg0MDliNzM4NzI3ZTk; gfkadpd=4842,34653; csrf_session_id=8852af3dea04a566c470cd8be4cab3cf; channel=0; isOaLinkedMpSecondLevel=false; isH5RevisitAppSecondLevel=false; isOaLinkedMpThirdLevel=false; isH5RevisitAppThirdLevel=false; oaLinkedMpName=; enableQuickApp=true; enableWechatH5=false; enableWechatApp=false; enableDouYinMp=false; enableDouYinBookMp=false; enableDouYinFreeSeriesMp=false; enableDouYinVipStoryMp=false; userAvatar=https://p3-passport.byteacctimg.com/img/mosaic-legacy/3796/2975850990~300x300.image; username=ms-hyh; loginType=0; adUserId=4233556857727276; rootAdUserId=4233556857727276; packageListAll=[object Object],[object Object],[object Object],[object Object],[object Object]; packageListDistributor=[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object]; mail=<EMAIL>; nickName=ms; role=1; isUg=false; available_packages=[object Object]; coopChannel=1; groupId=1811605586208939; novelRankingWhitelistGroupIds=1783251816647796,1839853445801994,1838611305421883,1779333388857431,1800913441504267,1793491136522252,1832812320750857,1796375234825290,1837061940208651,1796667487573011,1837342051503259,1835891620018234,1766666718380078,1835066179787786,1835067175260266,1754203208924206,1830278188337242,1754209980563518,1754209973991511,1754187880462343,1754209978065943,1754209973992567,1820218073380042,1800289047925770,1754209978896478,1754209978894446,1833967317368953,1834158042637403,1805370223243428,1754209973183495,1754209977248782,1754209973182503,1754206649820216,1754209973181463,1754206649821240,1754209973181479,1754209973992551,1754209978065927,1754209977248814,1754204938732552,1755358490161262,1809707920528409,1754209974785111,1754209953535021,1754209977249854,1754209978063895,1754209953534989,1754209973992535,1754209978894414,1795099876658218,1754209976474647,1781249619631163,1754209975628830,1820651659644010,1785313238959306,1759155630252055,1791490374093962,1754209953536045,1754209978894462,1787503093596283,1754206649821192,1786414392338442,1816317129354249,1832155119027369,1830619949858153,1826552594874443,1773738644600851,1800827795501098,1780115119080464,1815689411648585,1754209976472631,1754206649821224,1826203916530825,1754203208919144,1792306476310620,1754209983769655,1754209975626798,1820649751854155,1754209281593390,1754209982141496,1782067602072730,1816318218514506,1754209982141448,1781249619631163,1754209979751463,1762486742893629; appType=4; appId=30008812; appName=é‡‡é‡Œå‰§åœº; tt_scid=tj28nPGuWvErBQHDP0Lkjf9-9WV.kxASLxVEEm7jSZNETimOviNO5rol4xLdVgDR65cf; distributorName=é‡‡é‡Œå‰§åœº; distributorId=1827108343600299"
    a = text.encode('utf-8')
    print(a)