import requests
import re


url = "https://36kr.com"

# 模拟手机浏览器

# headers = {    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) "
#                   "AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1",}

# resp = requests.get(url,headers=headers)
# html = resp.text
# # print(html)
# # 将html保存到文件
# with open('36kr.html', 'w', encoding='utf-8') as f:
#     f.write(html)

# item_list = re.findall(r'<a class="item-info clearfloat" href="([^"]*), html)

# print(item_list)


def reg_36kr():
  resp = requests.get(url)
  html = resp.text
#   print(html)
  with open('36kr2.html', 'w', encoding='utf-8') as f:
    f.write(html)
  item_list = re.findall(r'<a class="article-item-title weight-bold" href="([^"]*)".*?>(.*?)</a>',html)
  print(item_list)


if __name__ == "__main__":
  reg_36kr()



