from DrissionPage import ChromiumPage, ChromiumOptions
import time

if __name__ == '__main__':

    co = ChromiumOptions()
    # 设置全屏
    co.set_argument('--start-maximized')

    page = ChromiumPage(co)

    page.get('https://www.baidu.com')
    time.sleep(1)

    page.ele('@id=kw').input('python')
    # page.ele('@id=chat-textarea').input('python')

    page.ele('@id=chat-submit-button').click()

    time.sleep(2)

    page.quit()
