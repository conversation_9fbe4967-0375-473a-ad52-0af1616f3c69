import requests
from lxml import etree

if __name__ == '__main__':
  url = 'https://movie.douban.com/subject/1292052/comments?status=P'
  headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36'
  }
  response = requests.get(url, headers=headers)
  # print(response.text)
  html = etree.HTML(response.text)
  spans = html.xpath('//span[@class="short"]/text()')
  for span in spans:
    print(span)
  # print(type(spans))
