import requests

url = "https://webapi.qingting.fm/api/mobile/rank/hotSaleWeekly"

headers = {
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"}

resp = requests.get(url,headers=headers)

rank_list = resp.json()['rankinglist']

# print(resp.text)
print(resp.status_code)
# print(resp.json()['rankinglist'])

for rank in rank_list:
  print(rank['title'])


