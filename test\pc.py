import hashlib
import time

def generate_signature(client_id: str, api_token: str, timestamp: int) -> str:
    """
    根据 client_id、api_token 和 timestamp 生成签名秘钥（小写md5(clientId+api_token+timestamp)）
    """
    # raw_str = f"{client_id}{api_token}{timestamp}"
    raw_str ="10003784BqvpPdhSLonMnJkmsn1752228790"
    return hashlib.md5(raw_str.encode('utf-8')).hexdigest()

timestamp = 1751265696722
client_id = "10003784"
api_token = "BqvpPdhSLonMnJkmsn"

signature = generate_signature(client_id, api_token, timestamp)

print("签名秘钥:", signature)
print("时间戳:", timestamp)