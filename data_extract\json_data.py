import jsonpath

info = {
    "error_code": 0,
    "stu_info": [
        {
            "id": 2059,
            "name": "小白",
            "sex": "男",
            "age": 28,
            "addr": "河南省济源市北海大道xx号",
            "grade": "天蝎座",
            "phone": "1837830xxxx",
            "gold": 10896,
            "info": {
                "card": ********,
                "bank_name": '中国银行'
            }
        },
        {
            "id": 2067,
            "name": "小黑",
            "sex": "男",
            "age": 28,
            "addr": "河南省济源市北海大道xx号",
            "grade": "天蝎座",
            "phone": "********",
            "gold": 100
        }
    ]
}


def execise():
    info = {
        "store": {
            "book": [
                {"category": "reference",
                "author": "<PERSON>",
                "title": "Sayings of the Century",
                "price": 8.95
                },
                {"category": "fiction",
                "author": "<PERSON>",
                "title": "Sword of Honour",
                "price": 12.99
                },
                {"category": "fiction",
                "author": "<PERSON>",
                "title": "Moby Dick",
                "isbn": "0-553-21311-3",
                "price": 8.99
                },
                {"category": "fiction",
                "author": "J. <PERSON>. <PERSON>. <PERSON>",
                "title": "The Lord of the Rings",
                "isbn": "0-395-19395-8",
                "price": 22.99
                }
            ],
            "bicycle": {
                "color": "red",
                "price": 19.95
            }
        }
    }
    # 1. 提取第1本书的title
    ret = jsonpath.jsonpath(info,'$..book[0].title')
    print('第一本书的title',ret)
    # 2. 提取2、3、4本书的标题
    ret = jsonpath.jsonpath(info,'$..book[1,2,3].title')
    print('2,3,4本书的标题',ret)
    ret = jsonpath.jsonpath(info,'$..book[1:].title')
    print('2,3,4本书的标题',ret)
    # 3. 提取1、3本书的标题
    ret = jsonpath.jsonpath(info,'$..book[1,3].title')
    print('1,3本书的标题',ret)

    # 4. 提取最后一本书的标题
    ret = jsonpath.jsonpath(info,'$..book[-1:].title')
    print('last book:',ret)
    ret = jsonpath.jsonpath(info,'$..book[(@.length-1)].title')
    print('last book:',ret)

    # 5. 提取价格小于10的书的标题
    ret = jsonpath.jsonpath(info,'$..book[?(@.price<10)]')
    print('price<10',ret)

    # 6. 提取价格小于或者等于20的所有商品的价格
    ret = jsonpath.jsonpath(info,'$..book[?(@.price<=20)].price')
    print('price<20 的 peice',ret)
    # 7. 获取所有书的作者
    # ret = jsonpath.jsonpath(info,'$..book.author') 在jsonpath中 如果book是一个数组，则需要使用数组访问语法
    # print('all author:',ret)
    print(jsonpath.jsonpath(info,'$..author'))
    print(jsonpath.jsonpath(info,'$..book[::].author'))
    # 8. 获取所有作者
    print(jsonpath.jsonpath(info,'$..author'))

    # 9. 获取在store中的所有商品(包括书、自行车)
    print('store',jsonpath.jsonpath(info,'$..store'))

    # 10. 获取所有商品（包括书、自行车）的价格
    print('all price',jsonpath.jsonpath(info,'$..price'))
    # 11. 获取带有isbn的书
    print('isbn is not null ',jsonpath.jsonpath(info,'$..book[?(@.isbn)]'))

    # 12. 获取不带有isbn的书
    print('isbn is null ',jsonpath.jsonpath(info,'$..book[?(!@.isbn)]'))

    # 13. 获取价格在5~10之间的书
    ret = jsonpath.jsonpath(info,'$..book[?(@.price > 5 && @.price < 10)]')
    print('price 5~ 10',ret)
    # 14. 获取价格不在5~10之间的书
    ret = jsonpath.jsonpath(info,'$..book[?(@.price < 5 || @.price > 10)]')
    print('price not 5~ 10',ret)

    # 15. 获取所有的元素
    print('all ele',jsonpath.jsonpath(info,'$..*'))

# res = jsonpath.jsonpath(info, '$..stu_info[?(@.gold<=100)]')
# res = jsonpath.jsonpath(info, '$.stu_info..addr')
# print(res)

if __name__ == '__main__':
    execise()

